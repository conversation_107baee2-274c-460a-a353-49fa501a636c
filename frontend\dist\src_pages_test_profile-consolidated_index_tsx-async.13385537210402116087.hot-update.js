globalThis.makoModuleHotUpdate('src/pages/test/profile-consolidated/index.tsx', {
    modules: {
        "src/pages/test/profile-consolidated/TeamListCard.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _team = __mako_require__("src/services/team.ts");
            var _services = __mako_require__("src/services/index.ts");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Text, Title } = _antd.Typography;
            // 添加内联样式
            const styles = `
  .team-item .ant-card-body {
    padding: 0 !important;
  }

  .team-name-hover:hover {
    color: #1890ff !important;
  }

  .team-switch-icon {
    transition: transform 0.2s ease;
  }

  .team-name-hover:hover .team-switch-icon {
    transform: translateX(2px);
  }

  @media (max-width: 768px) {
    .team-item {
      margin-bottom: 12px;
    }

    .team-stats-grid {
      grid-template-columns: repeat(2, 1fr) !important;
      gap: 8px !important;
    }
  }

  @media (max-width: 576px) {
    .team-stats-grid {
      grid-template-columns: 1fr !important;
    }
  }
`;
            const TeamListCard = ()=>{
                _s();
                // 团队列表状态管理
                const [teams, setTeams] = (0, _react.useState)([]);
                const [loading, setLoading] = (0, _react.useState)(true);
                const [error, setError] = (0, _react.useState)(null);
                const [switchingTeamId, setSwitchingTeamId] = (0, _react.useState)(null);
                const { initialState, setInitialState } = (0, _max.useModel)('@@initialState');
                const currentTeam = initialState === null || initialState === void 0 ? void 0 : initialState.currentTeam;
                // 获取团队列表数据
                (0, _react.useEffect)(()=>{
                    const fetchTeams = async ()=>{
                        try {
                            setLoading(true);
                            setError(null);
                            const teamsData = await _team.TeamService.getUserTeamsWithStats();
                            setTeams(teamsData);
                        } catch (error) {
                            console.error('获取团队列表失败:', error);
                            setError('获取团队列表失败');
                        } finally{
                            setLoading(false);
                        }
                    };
                    fetchTeams();
                }, []);
                // 团队切换处理函数
                const handleTeamSwitch = async (teamId, teamName)=>{
                    if (teamId === (currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id)) {
                        _antd.message.info('您已经在当前团队中');
                        return;
                    }
                    try {
                        setSwitchingTeamId(teamId);
                        const response = await _services.AuthService.selectTeam({
                            teamId
                        });
                        // 检查后端返回的团队选择成功标识
                        if (response.teamSelectionSuccess && response.team && response.team.id === teamId) {
                            _antd.message.success(`已切换到团队：${teamName}`);
                            // 同步更新 initialState，等待更新完成后再跳转
                            if ((initialState === null || initialState === void 0 ? void 0 : initialState.fetchTeamInfo) && (initialState === null || initialState === void 0 ? void 0 : initialState.fetchUserInfo) && setInitialState) try {
                                const [currentUser, currentTeam] = await Promise.all([
                                    initialState.fetchUserInfo(),
                                    initialState.fetchTeamInfo()
                                ]);
                                // 确保团队信息已正确获取
                                if (currentTeam && currentTeam.id === teamId) {
                                    await setInitialState({
                                        ...initialState,
                                        currentUser,
                                        currentTeam
                                    });
                                    // 等待 initialState 更新完成后再跳转到仪表盘
                                    setTimeout(()=>{
                                        _max.history.push('/dashboard');
                                    }, 100);
                                } else {
                                    console.error('获取的团队信息与选择的团队不匹配');
                                    _antd.message.error('团队切换失败，请重试');
                                }
                            } catch (error) {
                                console.error('更新 initialState 失败:', error);
                                _antd.message.error('团队切换失败，请重试');
                            }
                            else // 如果没有 initialState 相关方法，直接跳转
                            _max.history.push('/dashboard');
                        } else {
                            console.error('团队切换响应异常，未返回正确的团队信息');
                            _antd.message.error('团队切换失败，请重试');
                        }
                    } catch (error) {
                        console.error('团队切换失败:', error);
                        _antd.message.error('团队切换失败');
                    } finally{
                        setSwitchingTeamId(null);
                    }
                };
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("style", {
                            dangerouslySetInnerHTML: {
                                __html: styles
                            }
                        }, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                            lineNumber: 158,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            className: "dashboard-card",
                            style: {
                                borderRadius: 16,
                                boxShadow: "0 6px 20px rgba(0,0,0,0.08)",
                                border: "none",
                                background: "linear-gradient(145deg, #ffffff, #f8faff)"
                            },
                            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                justify: "space-between",
                                align: "center",
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                    level: 4,
                                    style: {
                                        margin: 0,
                                        background: 'linear-gradient(135deg, #1890ff, #722ed1)',
                                        WebkitBackgroundClip: 'text',
                                        WebkitTextFillColor: 'transparent',
                                        fontWeight: 600
                                    },
                                    children: "团队列表"
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                    lineNumber: 170,
                                    columnNumber: 13
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                lineNumber: 169,
                                columnNumber: 11
                            }, void 0),
                            children: error ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                                message: "团队列表加载失败",
                                description: error,
                                type: "error",
                                showIcon: true,
                                style: {
                                    marginBottom: 16
                                }
                            }, void 0, false, {
                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                lineNumber: 183,
                                columnNumber: 9
                            }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                                spinning: loading,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                                    dataSource: teams,
                                    renderItem: (item)=>{
                                        var _item_stats, _item_stats1, _item_stats2, _item_stats3;
                                        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                className: "team-item",
                                                style: {
                                                    background: (currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id) === item.id ? "linear-gradient(135deg, #f0f9ff, #e6f4ff)" : "#fff",
                                                    borderRadius: 16,
                                                    boxShadow: (currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id) === item.id ? "0 4px 16px rgba(24, 144, 255, 0.12)" : "0 2px 8px rgba(0,0,0,0.06)",
                                                    width: "100%",
                                                    borderLeft: `5px solid ${item.isCreator ? "#722ed1" : "#52c41a"}`,
                                                    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                                                    border: (currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id) === item.id ? "1px solid #91caff" : "1px solid #f0f0f0",
                                                    padding: "24px 28px",
                                                    position: 'relative',
                                                    overflow: 'hidden'
                                                },
                                                hoverable: true,
                                                onMouseEnter: (e)=>{
                                                    if ((currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id) !== item.id) {
                                                        e.currentTarget.style.transform = 'translateY(-2px)';
                                                        e.currentTarget.style.boxShadow = '0 8px 24px rgba(0,0,0,0.12)';
                                                    }
                                                },
                                                onMouseLeave: (e)=>{
                                                    if ((currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id) !== item.id) {
                                                        e.currentTarget.style.transform = 'translateY(0)';
                                                        e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.06)';
                                                    }
                                                },
                                                children: [
                                                    (currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id) === item.id && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            position: 'absolute',
                                                            top: 0,
                                                            right: 0,
                                                            width: '100px',
                                                            height: '100px',
                                                            background: 'linear-gradient(135deg, rgba(24, 144, 255, 0.05), rgba(64, 169, 255, 0.02))',
                                                            borderRadius: '0 16px 0 100px',
                                                            pointerEvents: 'none'
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                        lineNumber: 232,
                                                        columnNumber: 21
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                                        justify: "space-between",
                                                        align: "top",
                                                        gutter: [
                                                            20,
                                                            0
                                                        ],
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                                flex: "1",
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                    vertical: true,
                                                                    gap: 12,
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                            align: "center",
                                                                            justify: "space-between",
                                                                            style: {
                                                                                width: '100%'
                                                                            },
                                                                            children: [
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                                    align: "center",
                                                                                    gap: 12,
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                            style: {
                                                                                                position: 'relative',
                                                                                                cursor: (currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id) === item.id ? 'default' : 'pointer',
                                                                                                padding: '4px 0'
                                                                                            },
                                                                                            onClick: ()=>handleTeamSwitch(item.id, item.name),
                                                                                            children: [
                                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                                    strong: true,
                                                                                                    style: {
                                                                                                        fontSize: 20,
                                                                                                        color: (currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id) === item.id ? '#1890ff' : '#262626',
                                                                                                        lineHeight: 1.2,
                                                                                                        transition: 'all 0.2s ease',
                                                                                                        position: 'relative'
                                                                                                    },
                                                                                                    className: (currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id) !== item.id ? 'team-name-hover' : '',
                                                                                                    children: item.name
                                                                                                }, void 0, false, {
                                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                                    lineNumber: 260,
                                                                                                    columnNumber: 31
                                                                                                }, void 0),
                                                                                                (currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id) !== item.id && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.RightOutlined, {
                                                                                                    style: {
                                                                                                        fontSize: 12,
                                                                                                        color: '#8c8c8c',
                                                                                                        marginLeft: 8,
                                                                                                        transition: 'all 0.2s ease'
                                                                                                    },
                                                                                                    className: "team-switch-icon"
                                                                                                }, void 0, false, {
                                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                                    lineNumber: 274,
                                                                                                    columnNumber: 33
                                                                                                }, void 0)
                                                                                            ]
                                                                                        }, void 0, true, {
                                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                            lineNumber: 252,
                                                                                            columnNumber: 29
                                                                                        }, void 0),
                                                                                        (currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id) === item.id && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                            style: {
                                                                                                background: 'linear-gradient(135deg, #1890ff, #40a9ff)',
                                                                                                color: 'white',
                                                                                                padding: '4px 12px',
                                                                                                borderRadius: 20,
                                                                                                fontSize: 12,
                                                                                                fontWeight: 600,
                                                                                                display: 'flex',
                                                                                                alignItems: 'center',
                                                                                                gap: 4,
                                                                                                boxShadow: '0 2px 6px rgba(24, 144, 255, 0.3)'
                                                                                            },
                                                                                            children: [
                                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckCircleOutlined, {
                                                                                                    style: {
                                                                                                        fontSize: 12
                                                                                                    }
                                                                                                }, void 0, false, {
                                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                                    lineNumber: 300,
                                                                                                    columnNumber: 33
                                                                                                }, void 0),
                                                                                                "当前团队"
                                                                                            ]
                                                                                        }, void 0, true, {
                                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                            lineNumber: 288,
                                                                                            columnNumber: 31
                                                                                        }, void 0),
                                                                                        switchingTeamId === item.id && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                            style: {
                                                                                                background: '#f0f0f0',
                                                                                                padding: '6px 12px',
                                                                                                borderRadius: 20,
                                                                                                display: 'flex',
                                                                                                alignItems: 'center',
                                                                                                gap: 6
                                                                                            },
                                                                                            children: [
                                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                                                                                                    size: "small"
                                                                                                }, void 0, false, {
                                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                                    lineNumber: 315,
                                                                                                    columnNumber: 33
                                                                                                }, void 0),
                                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                                    style: {
                                                                                                        fontSize: 12,
                                                                                                        color: '#666'
                                                                                                    },
                                                                                                    children: "切换中..."
                                                                                                }, void 0, false, {
                                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                                    lineNumber: 316,
                                                                                                    columnNumber: 33
                                                                                                }, void 0)
                                                                                            ]
                                                                                        }, void 0, true, {
                                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                            lineNumber: 307,
                                                                                            columnNumber: 31
                                                                                        }, void 0)
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                    lineNumber: 250,
                                                                                    columnNumber: 27
                                                                                }, void 0),
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                    style: {
                                                                                        background: item.isCreator ? 'linear-gradient(135deg, #722ed1, #9254de)' : 'linear-gradient(135deg, #52c41a, #73d13d)',
                                                                                        color: 'white',
                                                                                        padding: '6px 14px',
                                                                                        borderRadius: 16,
                                                                                        fontSize: 12,
                                                                                        fontWeight: 600,
                                                                                        display: 'flex',
                                                                                        alignItems: 'center',
                                                                                        gap: 6,
                                                                                        boxShadow: item.isCreator ? '0 2px 8px rgba(114, 46, 209, 0.3)' : '0 2px 8px rgba(82, 196, 26, 0.3)',
                                                                                        minWidth: 'fit-content'
                                                                                    },
                                                                                    children: item.isCreator ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                                                                        children: [
                                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {
                                                                                                style: {
                                                                                                    fontSize: 14
                                                                                                }
                                                                                            }, void 0, false, {
                                                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                                lineNumber: 341,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            "管理员"
                                                                                        ]
                                                                                    }, void 0, true) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                                                                        children: [
                                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                                                                                style: {
                                                                                                    fontSize: 14
                                                                                                }
                                                                                            }, void 0, false, {
                                                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                                lineNumber: 346,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            "成员"
                                                                                        ]
                                                                                    }, void 0, true)
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                    lineNumber: 322,
                                                                                    columnNumber: 27
                                                                                }, void 0)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                            lineNumber: 249,
                                                                            columnNumber: 25
                                                                        }, void 0),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                            align: "center",
                                                                            gap: 32,
                                                                            style: {
                                                                                marginTop: 8
                                                                            },
                                                                            children: [
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                                                    title: `创建时间: ${new Date(item.createdAt).toLocaleString('zh-CN')}`,
                                                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                                        align: "center",
                                                                                        gap: 8,
                                                                                        children: [
                                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                                style: {
                                                                                                    background: 'linear-gradient(135deg, #f0f9ff, #e0f2fe)',
                                                                                                    padding: '4px',
                                                                                                    borderRadius: '6px',
                                                                                                    display: 'flex',
                                                                                                    alignItems: 'center'
                                                                                                },
                                                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {
                                                                                                    style: {
                                                                                                        color: "#0ea5e9",
                                                                                                        fontSize: 14
                                                                                                    }
                                                                                                }, void 0, false, {
                                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                                    lineNumber: 364,
                                                                                                    columnNumber: 33
                                                                                                }, void 0)
                                                                                            }, void 0, false, {
                                                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                                lineNumber: 357,
                                                                                                columnNumber: 31
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                                style: {
                                                                                                    fontSize: 13,
                                                                                                    color: '#595959',
                                                                                                    fontWeight: 500
                                                                                                },
                                                                                                children: [
                                                                                                    "创建于 ",
                                                                                                    new Date(item.createdAt).toLocaleDateString('zh-CN')
                                                                                                ]
                                                                                            }, void 0, true, {
                                                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                                lineNumber: 366,
                                                                                                columnNumber: 31
                                                                                            }, void 0)
                                                                                        ]
                                                                                    }, void 0, true, {
                                                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                        lineNumber: 356,
                                                                                        columnNumber: 29
                                                                                    }, void 0)
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                    lineNumber: 355,
                                                                                    columnNumber: 27
                                                                                }, void 0),
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                                                    title: `团队成员: ${item.memberCount}人`,
                                                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                                        align: "center",
                                                                                        gap: 8,
                                                                                        children: [
                                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                                style: {
                                                                                                    background: 'linear-gradient(135deg, #f0f9ff, #e0f2fe)',
                                                                                                    padding: '4px',
                                                                                                    borderRadius: '6px',
                                                                                                    display: 'flex',
                                                                                                    alignItems: 'center'
                                                                                                },
                                                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                                                                                    style: {
                                                                                                        color: "#0ea5e9",
                                                                                                        fontSize: 14
                                                                                                    }
                                                                                                }, void 0, false, {
                                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                                    lineNumber: 381,
                                                                                                    columnNumber: 33
                                                                                                }, void 0)
                                                                                            }, void 0, false, {
                                                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                                lineNumber: 374,
                                                                                                columnNumber: 31
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                                style: {
                                                                                                    fontSize: 13,
                                                                                                    color: '#595959',
                                                                                                    fontWeight: 500
                                                                                                },
                                                                                                children: [
                                                                                                    item.memberCount,
                                                                                                    " 名成员"
                                                                                                ]
                                                                                            }, void 0, true, {
                                                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                                lineNumber: 383,
                                                                                                columnNumber: 31
                                                                                            }, void 0)
                                                                                        ]
                                                                                    }, void 0, true, {
                                                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                        lineNumber: 373,
                                                                                        columnNumber: 29
                                                                                    }, void 0)
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                    lineNumber: 372,
                                                                                    columnNumber: 27
                                                                                }, void 0)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                            lineNumber: 354,
                                                                            columnNumber: 25
                                                                        }, void 0)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                    lineNumber: 247,
                                                                    columnNumber: 23
                                                                }, void 0)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                lineNumber: 246,
                                                                columnNumber: 21
                                                            }, void 0),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                                flex: "none",
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    className: "team-stats-grid",
                                                                    style: {
                                                                        display: 'grid',
                                                                        gridTemplateColumns: 'repeat(2, 1fr)',
                                                                        gap: '12px',
                                                                        minWidth: '200px'
                                                                    },
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                            style: {
                                                                                background: 'linear-gradient(135deg, #e6f4ff, #f0f9ff)',
                                                                                border: '1px solid #91caff',
                                                                                borderRadius: 12,
                                                                                padding: '12px',
                                                                                textAlign: 'center',
                                                                                transition: 'all 0.3s ease',
                                                                                cursor: 'pointer',
                                                                                position: 'relative',
                                                                                overflow: 'hidden'
                                                                            },
                                                                            onMouseEnter: (e)=>{
                                                                                e.currentTarget.style.transform = 'translateY(-2px)';
                                                                                e.currentTarget.style.boxShadow = '0 4px 12px rgba(24, 144, 255, 0.15)';
                                                                            },
                                                                            onMouseLeave: (e)=>{
                                                                                e.currentTarget.style.transform = 'translateY(0)';
                                                                                e.currentTarget.style.boxShadow = 'none';
                                                                            },
                                                                            children: [
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                    style: {
                                                                                        position: 'absolute',
                                                                                        top: 0,
                                                                                        right: 0,
                                                                                        width: '30px',
                                                                                        height: '30px',
                                                                                        background: 'linear-gradient(135deg, #1890ff, #40a9ff)',
                                                                                        borderRadius: '0 12px 0 20px',
                                                                                        opacity: 0.1
                                                                                    }
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                    lineNumber: 422,
                                                                                    columnNumber: 27
                                                                                }, void 0),
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                                    vertical: true,
                                                                                    align: "center",
                                                                                    gap: 6,
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                            style: {
                                                                                                background: 'linear-gradient(135deg, #1890ff, #40a9ff)',
                                                                                                borderRadius: '50%',
                                                                                                width: '32px',
                                                                                                height: '32px',
                                                                                                display: 'flex',
                                                                                                alignItems: 'center',
                                                                                                justifyContent: 'center',
                                                                                                marginBottom: '4px'
                                                                                            },
                                                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CarOutlined, {
                                                                                                style: {
                                                                                                    color: "white",
                                                                                                    fontSize: 16
                                                                                                }
                                                                                            }, void 0, false, {
                                                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                                lineNumber: 443,
                                                                                                columnNumber: 31
                                                                                            }, void 0)
                                                                                        }, void 0, false, {
                                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                            lineNumber: 433,
                                                                                            columnNumber: 29
                                                                                        }, void 0),
                                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                            strong: true,
                                                                                            style: {
                                                                                                fontSize: 18,
                                                                                                color: '#1890ff',
                                                                                                lineHeight: 1
                                                                                            },
                                                                                            children: ((_item_stats = item.stats) === null || _item_stats === void 0 ? void 0 : _item_stats.vehicles) || 0
                                                                                        }, void 0, false, {
                                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                            lineNumber: 445,
                                                                                            columnNumber: 29
                                                                                        }, void 0),
                                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                            style: {
                                                                                                fontSize: 11,
                                                                                                color: '#666',
                                                                                                fontWeight: 500
                                                                                            },
                                                                                            children: "车辆资源"
                                                                                        }, void 0, false, {
                                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                            lineNumber: 448,
                                                                                            columnNumber: 29
                                                                                        }, void 0)
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                    lineNumber: 432,
                                                                                    columnNumber: 27
                                                                                }, void 0)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                            lineNumber: 403,
                                                                            columnNumber: 25
                                                                        }, void 0),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                            style: {
                                                                                background: 'linear-gradient(135deg, #f6ffed, #f0f9ff)',
                                                                                border: '1px solid #95de64',
                                                                                borderRadius: 12,
                                                                                padding: '12px',
                                                                                textAlign: 'center',
                                                                                transition: 'all 0.3s ease',
                                                                                cursor: 'pointer',
                                                                                position: 'relative',
                                                                                overflow: 'hidden'
                                                                            },
                                                                            onMouseEnter: (e)=>{
                                                                                e.currentTarget.style.transform = 'translateY(-2px)';
                                                                                e.currentTarget.style.boxShadow = '0 4px 12px rgba(82, 196, 26, 0.15)';
                                                                            },
                                                                            onMouseLeave: (e)=>{
                                                                                e.currentTarget.style.transform = 'translateY(0)';
                                                                                e.currentTarget.style.boxShadow = 'none';
                                                                            },
                                                                            children: [
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                    style: {
                                                                                        position: 'absolute',
                                                                                        top: 0,
                                                                                        right: 0,
                                                                                        width: '30px',
                                                                                        height: '30px',
                                                                                        background: 'linear-gradient(135deg, #52c41a, #73d13d)',
                                                                                        borderRadius: '0 12px 0 20px',
                                                                                        opacity: 0.1
                                                                                    }
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                    lineNumber: 472,
                                                                                    columnNumber: 27
                                                                                }, void 0),
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                                    vertical: true,
                                                                                    align: "center",
                                                                                    gap: 6,
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                            style: {
                                                                                                background: 'linear-gradient(135deg, #52c41a, #73d13d)',
                                                                                                borderRadius: '50%',
                                                                                                width: '32px',
                                                                                                height: '32px',
                                                                                                display: 'flex',
                                                                                                alignItems: 'center',
                                                                                                justifyContent: 'center',
                                                                                                marginBottom: '4px'
                                                                                            },
                                                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                                                                                style: {
                                                                                                    color: "white",
                                                                                                    fontSize: 16
                                                                                                }
                                                                                            }, void 0, false, {
                                                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                                lineNumber: 493,
                                                                                                columnNumber: 31
                                                                                            }, void 0)
                                                                                        }, void 0, false, {
                                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                            lineNumber: 483,
                                                                                            columnNumber: 29
                                                                                        }, void 0),
                                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                            strong: true,
                                                                                            style: {
                                                                                                fontSize: 18,
                                                                                                color: '#52c41a',
                                                                                                lineHeight: 1
                                                                                            },
                                                                                            children: ((_item_stats1 = item.stats) === null || _item_stats1 === void 0 ? void 0 : _item_stats1.personnel) || 0
                                                                                        }, void 0, false, {
                                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                            lineNumber: 495,
                                                                                            columnNumber: 29
                                                                                        }, void 0),
                                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                            style: {
                                                                                                fontSize: 11,
                                                                                                color: '#666',
                                                                                                fontWeight: 500
                                                                                            },
                                                                                            children: "人员资源"
                                                                                        }, void 0, false, {
                                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                            lineNumber: 498,
                                                                                            columnNumber: 29
                                                                                        }, void 0)
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                    lineNumber: 482,
                                                                                    columnNumber: 27
                                                                                }, void 0)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                            lineNumber: 453,
                                                                            columnNumber: 25
                                                                        }, void 0),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                            style: {
                                                                                background: 'linear-gradient(135deg, #fff7e6, #fffbe6)',
                                                                                border: '1px solid #ffd666',
                                                                                borderRadius: 12,
                                                                                padding: '12px',
                                                                                textAlign: 'center',
                                                                                transition: 'all 0.3s ease',
                                                                                cursor: 'pointer',
                                                                                position: 'relative',
                                                                                overflow: 'hidden'
                                                                            },
                                                                            onMouseEnter: (e)=>{
                                                                                e.currentTarget.style.transform = 'translateY(-2px)';
                                                                                e.currentTarget.style.boxShadow = '0 4px 12px rgba(250, 173, 20, 0.15)';
                                                                            },
                                                                            onMouseLeave: (e)=>{
                                                                                e.currentTarget.style.transform = 'translateY(0)';
                                                                                e.currentTarget.style.boxShadow = 'none';
                                                                            },
                                                                            children: [
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                    style: {
                                                                                        position: 'absolute',
                                                                                        top: 0,
                                                                                        right: 0,
                                                                                        width: '30px',
                                                                                        height: '30px',
                                                                                        background: 'linear-gradient(135deg, #faad14, #ffc53d)',
                                                                                        borderRadius: '0 12px 0 20px',
                                                                                        opacity: 0.1
                                                                                    }
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                    lineNumber: 522,
                                                                                    columnNumber: 27
                                                                                }, void 0),
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                                    vertical: true,
                                                                                    align: "center",
                                                                                    gap: 6,
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                            style: {
                                                                                                background: 'linear-gradient(135deg, #faad14, #ffc53d)',
                                                                                                borderRadius: '50%',
                                                                                                width: '32px',
                                                                                                height: '32px',
                                                                                                display: 'flex',
                                                                                                alignItems: 'center',
                                                                                                justifyContent: 'center',
                                                                                                marginBottom: '4px'
                                                                                            },
                                                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                                                                                                style: {
                                                                                                    color: "white",
                                                                                                    fontSize: 16
                                                                                                }
                                                                                            }, void 0, false, {
                                                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                                lineNumber: 543,
                                                                                                columnNumber: 31
                                                                                            }, void 0)
                                                                                        }, void 0, false, {
                                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                            lineNumber: 533,
                                                                                            columnNumber: 29
                                                                                        }, void 0),
                                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                            strong: true,
                                                                                            style: {
                                                                                                fontSize: 18,
                                                                                                color: '#faad14',
                                                                                                lineHeight: 1
                                                                                            },
                                                                                            children: ((_item_stats2 = item.stats) === null || _item_stats2 === void 0 ? void 0 : _item_stats2.expiring) || 0
                                                                                        }, void 0, false, {
                                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                            lineNumber: 545,
                                                                                            columnNumber: 29
                                                                                        }, void 0),
                                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                            style: {
                                                                                                fontSize: 11,
                                                                                                color: '#666',
                                                                                                fontWeight: 500
                                                                                            },
                                                                                            children: "临期事项"
                                                                                        }, void 0, false, {
                                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                            lineNumber: 548,
                                                                                            columnNumber: 29
                                                                                        }, void 0)
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                    lineNumber: 532,
                                                                                    columnNumber: 27
                                                                                }, void 0)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                            lineNumber: 503,
                                                                            columnNumber: 25
                                                                        }, void 0),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                            style: {
                                                                                background: 'linear-gradient(135deg, #fff1f0, #fff2f0)',
                                                                                border: '1px solid #ffadd2',
                                                                                borderRadius: 12,
                                                                                padding: '12px',
                                                                                textAlign: 'center',
                                                                                transition: 'all 0.3s ease',
                                                                                cursor: 'pointer',
                                                                                position: 'relative',
                                                                                overflow: 'hidden'
                                                                            },
                                                                            onMouseEnter: (e)=>{
                                                                                e.currentTarget.style.transform = 'translateY(-2px)';
                                                                                e.currentTarget.style.boxShadow = '0 4px 12px rgba(255, 77, 79, 0.15)';
                                                                            },
                                                                            onMouseLeave: (e)=>{
                                                                                e.currentTarget.style.transform = 'translateY(0)';
                                                                                e.currentTarget.style.boxShadow = 'none';
                                                                            },
                                                                            children: [
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                    style: {
                                                                                        position: 'absolute',
                                                                                        top: 0,
                                                                                        right: 0,
                                                                                        width: '30px',
                                                                                        height: '30px',
                                                                                        background: 'linear-gradient(135deg, #ff4d4f, #ff7875)',
                                                                                        borderRadius: '0 12px 0 20px',
                                                                                        opacity: 0.1
                                                                                    }
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                    lineNumber: 572,
                                                                                    columnNumber: 27
                                                                                }, void 0),
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                                    vertical: true,
                                                                                    align: "center",
                                                                                    gap: 6,
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                            style: {
                                                                                                background: 'linear-gradient(135deg, #ff4d4f, #ff7875)',
                                                                                                borderRadius: '50%',
                                                                                                width: '32px',
                                                                                                height: '32px',
                                                                                                display: 'flex',
                                                                                                alignItems: 'center',
                                                                                                justifyContent: 'center',
                                                                                                marginBottom: '4px'
                                                                                            },
                                                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                                                                                                style: {
                                                                                                    color: "white",
                                                                                                    fontSize: 16
                                                                                                }
                                                                                            }, void 0, false, {
                                                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                                lineNumber: 593,
                                                                                                columnNumber: 31
                                                                                            }, void 0)
                                                                                        }, void 0, false, {
                                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                            lineNumber: 583,
                                                                                            columnNumber: 29
                                                                                        }, void 0),
                                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                            strong: true,
                                                                                            style: {
                                                                                                fontSize: 18,
                                                                                                color: '#ff4d4f',
                                                                                                lineHeight: 1
                                                                                            },
                                                                                            children: ((_item_stats3 = item.stats) === null || _item_stats3 === void 0 ? void 0 : _item_stats3.overdue) || 0
                                                                                        }, void 0, false, {
                                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                            lineNumber: 595,
                                                                                            columnNumber: 29
                                                                                        }, void 0),
                                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                            style: {
                                                                                                fontSize: 11,
                                                                                                color: '#666',
                                                                                                fontWeight: 500
                                                                                            },
                                                                                            children: "逾期事项"
                                                                                        }, void 0, false, {
                                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                            lineNumber: 598,
                                                                                            columnNumber: 29
                                                                                        }, void 0)
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                    lineNumber: 582,
                                                                                    columnNumber: 27
                                                                                }, void 0)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                            lineNumber: 553,
                                                                            columnNumber: 25
                                                                        }, void 0)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                    lineNumber: 394,
                                                                    columnNumber: 23
                                                                }, void 0)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                lineNumber: 393,
                                                                columnNumber: 21
                                                            }, void 0)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                        lineNumber: 244,
                                                        columnNumber: 19
                                                    }, void 0)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                lineNumber: 196,
                                                columnNumber: 17
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                            lineNumber: 195,
                                            columnNumber: 15
                                        }, void 0);
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                    lineNumber: 192,
                                    columnNumber: 11
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                lineNumber: 191,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                            lineNumber: 160,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true);
            };
            _s(TeamListCard, "4DmFk5I0DN4oNd3BYpKpdN0lgTI=", false, function() {
                return [
                    _max.useModel
                ];
            });
            _c = TeamListCard;
            var _default = TeamListCard;
            var _c;
            $RefreshReg$(_c, "TeamListCard");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '6005269231393793614';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/.umi/plugin-openapi/openapi.tsx": [
            "vendors",
            "src/.umi/plugin-openapi/openapi.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/friend/index.tsx": [
            "p__friend__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "common",
            "p__subscription__index"
        ],
        "src/pages/team/detail/index.tsx": [
            "common",
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "p__team__index"
        ],
        "src/pages/test/profile-consolidated/index.tsx": [
            "src/pages/test/profile-consolidated/index.tsx"
        ],
        "src/pages/user/index.tsx": [
            "common",
            "p__user__index"
        ],
        "src/pages/user/login/index.tsx": [
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_test_profile-consolidated_index_tsx-async.13385537210402116087.hot-update.js.map