{"version": 3, "sources": ["src_pages_test_profile-consolidated_index_tsx-async.5046354040715242233.hot-update.js", "src/pages/test/profile-consolidated/TeamListCard.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/test/profile-consolidated/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='13782744681863514026';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/friend/index.tsx\":[\"p__friend__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"common\",\"p__subscription__index\"],\"src/pages/team/detail/index.tsx\":[\"common\",\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"p__team__index\"],\"src/pages/test/profile-consolidated/index.tsx\":[\"src/pages/test/profile-consolidated/index.tsx\"],\"src/pages/user/index.tsx\":[\"common\",\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import React, { useState, useEffect } from \"react\";\r\nimport {\r\n  Card,\r\n  Typography,\r\n  Tooltip,\r\n  List,\r\n  Flex,\r\n  Spin,\r\n  Alert,\r\n  message\r\n} from \"antd\";\r\nimport { TeamService } from \"@/services/team\";\r\nimport { AuthService } from \"@/services\";\r\nimport { useModel, history } from '@umijs/max';\r\nimport type { TeamDetailResponse } from \"@/types/api\";\r\nimport {\r\n  CarOutlined,\r\n  TeamOutlined,\r\n  UserOutlined,\r\n  ClockCircleOutlined,\r\n  CrownOutlined,\r\n  RightOutlined,\r\n  ExclamationCircleOutlined\r\n} from \"@ant-design/icons\";\r\n\r\nconst { Text, Title } = Typography;\r\n\r\n// 紧凑型布局样式\r\nconst styles = `\r\n  .team-item .ant-card-body {\r\n    padding: 0 !important;\r\n  }\r\n\r\n  .team-item:hover {\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    .team-item {\r\n      margin-bottom: 8px;\r\n    }\r\n  }\r\n\r\n  @media (max-width: 576px) {\r\n    .team-stats-flex {\r\n      flex-wrap: wrap;\r\n      gap: 4px !important;\r\n    }\r\n  }\r\n`;\r\n\r\nconst TeamListCard: React.FC = () => {\r\n  // 团队列表状态管理\r\n  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [switchingTeamId, setSwitchingTeamId] = useState<number | null>(null);\r\n\r\n  const { initialState, setInitialState } = useModel('@@initialState');\r\n  const currentTeam = initialState?.currentTeam;\r\n\r\n  // 获取团队列表数据\r\n  useEffect(() => {\r\n    const fetchTeams = async () => {\r\n      try {\r\n        setLoading(true);\r\n        setError(null);\r\n        const teamsData = await TeamService.getUserTeamsWithStats();\r\n        setTeams(teamsData);\r\n      } catch (error) {\r\n        console.error('获取团队列表失败:', error);\r\n        setError('获取团队列表失败');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchTeams();\r\n  }, []);\r\n\r\n  // 团队切换处理函数\r\n  const handleTeamSwitch = async (teamId: number, teamName: string) => {\r\n    try {\r\n      setSwitchingTeamId(teamId);\r\n\r\n      // 如果是当前团队，直接跳转到仪表盘，不需要调用切换API\r\n      if (teamId === currentTeam?.id) {\r\n        message.success(`进入团队：${teamName}`);\r\n        history.push('/dashboard');\r\n        return;\r\n      }\r\n\r\n      // 非当前团队，执行切换逻辑\r\n      const response = await AuthService.selectTeam({ teamId });\r\n\r\n      // 检查后端返回的团队选择成功标识\r\n      if (response.teamSelectionSuccess && response.team && response.team.id === teamId) {\r\n        message.success(`已切换到团队：${teamName}`);\r\n\r\n        // 同步更新 initialState，等待更新完成后再跳转\r\n        if (initialState?.fetchTeamInfo && initialState?.fetchUserInfo && setInitialState) {\r\n          try {\r\n            const [currentUser, currentTeam] = await Promise.all([\r\n              initialState.fetchUserInfo(),\r\n              initialState.fetchTeamInfo()\r\n            ]);\r\n\r\n            // 确保团队信息已正确获取\r\n            if (currentTeam && currentTeam.id === teamId) {\r\n              await setInitialState({\r\n                ...initialState,\r\n                currentUser,\r\n                currentTeam,\r\n              });\r\n\r\n              // 等待 initialState 更新完成后再跳转到仪表盘\r\n              setTimeout(() => {\r\n                history.push('/dashboard');\r\n              }, 100);\r\n            } else {\r\n              console.error('获取的团队信息与选择的团队不匹配');\r\n              message.error('团队切换失败，请重试');\r\n            }\r\n          } catch (error) {\r\n            console.error('更新 initialState 失败:', error);\r\n            message.error('团队切换失败，请重试');\r\n          }\r\n        } else {\r\n          // 如果没有 initialState 相关方法，直接跳转\r\n          history.push('/dashboard');\r\n        }\r\n      } else {\r\n        console.error('团队切换响应异常，未返回正确的团队信息');\r\n        message.error('团队切换失败，请重试');\r\n      }\r\n    } catch (error) {\r\n      console.error('团队切换失败:', error);\r\n      message.error('团队切换失败');\r\n    } finally {\r\n      setSwitchingTeamId(null);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* 注入样式 */}\r\n      <style dangerouslySetInnerHTML={{ __html: styles }} />\r\n\r\n      <Card\r\n        className=\"dashboard-card\"\r\n        style={{\r\n          borderRadius: 16,\r\n          boxShadow: \"0 6px 20px rgba(0,0,0,0.08)\",\r\n          border: \"none\",\r\n          background: \"linear-gradient(145deg, #ffffff, #f8faff)\",\r\n        }}\r\n        title={\r\n          <Flex justify=\"space-between\" align=\"center\">\r\n            <Title level={4} style={{\r\n              margin: 0,\r\n              background: 'linear-gradient(135deg, #1890ff, #722ed1)',\r\n              WebkitBackgroundClip: 'text',\r\n              WebkitTextFillColor: 'transparent',\r\n              fontWeight: 600\r\n            }}>\r\n              团队列表\r\n            </Title>\r\n          </Flex>\r\n        }\r\n      >\r\n      {error ? (\r\n        <Alert\r\n          message=\"团队列表加载失败\"\r\n          description={error}\r\n          type=\"error\"\r\n          showIcon\r\n          style={{ marginBottom: 16 }}\r\n        />\r\n      ) : (\r\n        <Spin spinning={loading}>\r\n          <List\r\n            dataSource={teams}\r\n            renderItem={(item) => (\r\n              <List.Item>\r\n                <Card\r\n                  className=\"team-item\"\r\n                  style={{\r\n                    background: currentTeam?.id === item.id\r\n                      ? \"linear-gradient(135deg, #f0f9ff, #e6f4ff)\"\r\n                      : \"#fff\",\r\n                    borderRadius: 8,\r\n                    boxShadow: currentTeam?.id === item.id\r\n                      ? \"0 2px 8px rgba(24, 144, 255, 0.12)\"\r\n                      : \"0 1px 4px rgba(0,0,0,0.06)\",\r\n                    width: \"100%\",\r\n                    borderLeft: `3px solid ${item.isCreator ? \"#722ed1\" : \"#52c41a\"}`,\r\n                    transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\r\n                    border: currentTeam?.id === item.id\r\n                      ? \"1px solid #91caff\"\r\n                      : \"1px solid #f0f0f0\",\r\n                    padding: \"12px 16px\",\r\n                    position: 'relative',\r\n                    overflow: 'hidden'\r\n                  }}\r\n                  hoverable\r\n                  onMouseEnter={(e) => {\r\n                    if (currentTeam?.id !== item.id) {\r\n                      e.currentTarget.style.transform = 'translateY(-2px)';\r\n                      e.currentTarget.style.boxShadow = '0 8px 24px rgba(0,0,0,0.12)';\r\n                    }\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    if (currentTeam?.id !== item.id) {\r\n                      e.currentTarget.style.transform = 'translateY(0)';\r\n                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.06)';\r\n                    }\r\n                  }}\r\n                >\r\n\r\n                  {/* 紧凑型布局 */}\r\n                  <Flex justify=\"space-between\" align=\"center\" style={{ width: '100%' }}>\r\n                    {/* 左侧：团队信息 */}\r\n                    <Flex vertical gap={6} style={{ flex: 1 }}>\r\n                      {/* 团队名称行 */}\r\n                      <Flex align=\"center\" gap={8}>\r\n                        <div\r\n                          style={{\r\n                            cursor: currentTeam?.id === item.id ? 'default' : 'pointer',\r\n                            padding: '2px 4px',\r\n                            borderRadius: 4,\r\n                            transition: 'all 0.2s ease',\r\n                            display: 'flex',\r\n                            alignItems: 'center',\r\n                            gap: 6\r\n                          }}\r\n                          onClick={() => handleTeamSwitch(item.id, item.name)}\r\n                          onMouseEnter={(e) => {\r\n                            if (currentTeam?.id !== item.id) {\r\n                              e.currentTarget.style.background = 'rgba(24, 144, 255, 0.05)';\r\n                            }\r\n                          }}\r\n                          onMouseLeave={(e) => {\r\n                            if (currentTeam?.id !== item.id) {\r\n                              e.currentTarget.style.background = 'transparent';\r\n                            }\r\n                          }}\r\n                        >\r\n                          <Text\r\n                            strong\r\n                            style={{\r\n                              fontSize: 16,\r\n                              color: currentTeam?.id === item.id ? '#1890ff' : '#262626',\r\n                              lineHeight: 1.2\r\n                            }}\r\n                          >\r\n                            {item.name}\r\n                          </Text>\r\n                          <RightOutlined\r\n                            style={{\r\n                              fontSize: 10,\r\n                              color: currentTeam?.id === item.id ? '#1890ff' : '#8c8c8c',\r\n                              verticalAlign: 'middle',\r\n                              display: 'inline-flex',\r\n                              alignItems: 'center'\r\n                            }}\r\n                          />\r\n                        </div>\r\n\r\n                        {/* 状态标识 */}\r\n                        {currentTeam?.id === item.id && (\r\n                          <span style={{\r\n                            background: '#1890ff',\r\n                            color: 'white',\r\n                            padding: '1px 6px',\r\n                            borderRadius: 8,\r\n                            fontSize: 10,\r\n                            fontWeight: 500\r\n                          }}>\r\n                            当前\r\n                          </span>\r\n                        )}\r\n\r\n                        {switchingTeamId === item.id && (\r\n                          <Flex align=\"center\" gap={4}>\r\n                            <Spin size=\"small\" />\r\n                            <Text style={{ fontSize: 10, color: '#666' }}>切换中</Text>\r\n                          </Flex>\r\n                        )}\r\n                      </Flex>\r\n\r\n                      {/* 团队基本信息 */}\r\n                      <Flex align=\"center\" gap={16}>\r\n                        <Tooltip title={`创建时间: ${new Date(item.createdAt).toLocaleString('zh-CN')}`}>\r\n                          <Flex align=\"center\" gap={4}>\r\n                            <ClockCircleOutlined style={{ color: \"#8c8c8c\", fontSize: 12 }} />\r\n                            <Text style={{ fontSize: 12, color: '#8c8c8c' }}>\r\n                              {new Date(item.createdAt).toLocaleDateString('zh-CN')}\r\n                            </Text>\r\n                          </Flex>\r\n                        </Tooltip>\r\n\r\n                        <Tooltip title={`团队成员: ${item.memberCount}人`}>\r\n                          <Flex align=\"center\" gap={4}>\r\n                            <TeamOutlined style={{ color: \"#8c8c8c\", fontSize: 12 }} />\r\n                            <Text style={{ fontSize: 12, color: '#8c8c8c' }}>\r\n                              {item.memberCount} 人\r\n                            </Text>\r\n                          </Flex>\r\n                        </Tooltip>\r\n\r\n                        {/* 角色标识 - 移动到人员数量后面 */}\r\n                        <span style={{\r\n                          background: item.isCreator ? '#722ed1' : '#52c41a',\r\n                          color: 'white',\r\n                          padding: '2px 6px',\r\n                          borderRadius: 8,\r\n                          fontSize: 10,\r\n                          fontWeight: 500,\r\n                          display: 'flex',\r\n                          alignItems: 'center',\r\n                          gap: 2\r\n                        }}>\r\n                          {item.isCreator ? (\r\n                            <>\r\n                              <CrownOutlined style={{ fontSize: 9 }} />\r\n                              管理员\r\n                            </>\r\n                          ) : (\r\n                            <>\r\n                              <UserOutlined style={{ fontSize: 9 }} />\r\n                              成员\r\n                            </>\r\n                          )}\r\n                        </span>\r\n                      </Flex>\r\n                    </Flex>\r\n\r\n                    {/* 右侧：紧凑型指标卡片 */}\r\n                    <Flex gap={8} align=\"center\">\r\n                      {/* 车辆资源 */}\r\n                      <div style={{\r\n                        background: '#f0f7ff',\r\n                        border: '1px solid #d9e8ff',\r\n                        borderRadius: 6,\r\n                        padding: '6px 8px',\r\n                        textAlign: 'center',\r\n                        minWidth: '50px'\r\n                      }}>\r\n                        <Flex vertical align=\"center\" gap={2}>\r\n                          <CarOutlined style={{ color: \"#1890ff\", fontSize: 14 }} />\r\n                          <Text strong style={{ fontSize: 16, color: '#1890ff', lineHeight: 1 }}>\r\n                            {item.stats?.vehicles || 0}\r\n                          </Text>\r\n                          <Text style={{ fontSize: 9, color: '#666' }}>车辆</Text>\r\n                        </Flex>\r\n                      </div>\r\n\r\n                      {/* 人员资源 */}\r\n                      <div style={{\r\n                        background: '#f6ffed',\r\n                        border: '1px solid #d1f0be',\r\n                        borderRadius: 6,\r\n                        padding: '6px 8px',\r\n                        textAlign: 'center',\r\n                        minWidth: '50px'\r\n                      }}>\r\n                        <Flex vertical align=\"center\" gap={2}>\r\n                          <UserOutlined style={{ color: \"#52c41a\", fontSize: 14 }} />\r\n                          <Text strong style={{ fontSize: 16, color: '#52c41a', lineHeight: 1 }}>\r\n                            {item.stats?.personnel || 0}\r\n                          </Text>\r\n                          <Text style={{ fontSize: 9, color: '#666' }}>人员</Text>\r\n                        </Flex>\r\n                      </div>\r\n\r\n                      {/* 临期事项 */}\r\n                      <div style={{\r\n                        background: '#fff7e6',\r\n                        border: '1px solid #ffd666',\r\n                        borderRadius: 6,\r\n                        padding: '6px 8px',\r\n                        textAlign: 'center',\r\n                        minWidth: '50px'\r\n                      }}>\r\n                        <Flex vertical align=\"center\" gap={2}>\r\n                          <ExclamationCircleOutlined style={{ color: \"#faad14\", fontSize: 14 }} />\r\n                          <Text strong style={{ fontSize: 16, color: '#faad14', lineHeight: 1 }}>\r\n                            {item.stats?.expiring || 0}\r\n                          </Text>\r\n                          <Text style={{ fontSize: 9, color: '#666' }}>临期</Text>\r\n                        </Flex>\r\n                      </div>\r\n\r\n                      {/* 逾期事项 */}\r\n                      <div style={{\r\n                        background: '#fff1f0',\r\n                        border: '1px solid #ffccc7',\r\n                        borderRadius: 6,\r\n                        padding: '6px 8px',\r\n                        textAlign: 'center',\r\n                        minWidth: '50px'\r\n                      }}>\r\n                        <Flex vertical align=\"center\" gap={2}>\r\n                          <ExclamationCircleOutlined style={{ color: \"#ff4d4f\", fontSize: 14 }} />\r\n                          <Text strong style={{ fontSize: 16, color: '#ff4d4f', lineHeight: 1 }}>\r\n                            {item.stats?.overdue || 0}\r\n                          </Text>\r\n                          <Text style={{ fontSize: 9, color: '#666' }}>逾期</Text>\r\n                        </Flex>\r\n                      </div>\r\n                    </Flex>\r\n                  </Flex>\r\n                </Card>\r\n              </List.Item>\r\n            )}\r\n          />\r\n        </Spin>\r\n      )}\r\n    </Card>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default TeamListCard;"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,iDACA;IACE,SAAS;;;;;;wCCqab;;;2BAAA;;;;;;oFAxa2C;yCAUpC;yCACqB;6CACA;wCACM;0CAU3B;;;;;;;;;;YAEP,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,gBAAU;YAElC,UAAU;YACV,MAAM,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;AAsBhB,CAAC;YAED,MAAM,eAAyB;;gBAC7B,WAAW;gBACX,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAuB,EAAE;gBAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAgB;gBAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAgB;gBAEtE,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;gBACnD,MAAM,cAAc,yBAAA,mCAAA,aAAc,WAAW;gBAE7C,WAAW;gBACX,IAAA,gBAAS,EAAC;oBACR,MAAM,aAAa;wBACjB,IAAI;4BACF,WAAW;4BACX,SAAS;4BACT,MAAM,YAAY,MAAM,iBAAW,CAAC,qBAAqB;4BACzD,SAAS;wBACX,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,aAAa;4BAC3B,SAAS;wBACX,SAAU;4BACR,WAAW;wBACb;oBACF;oBAEA;gBACF,GAAG,EAAE;gBAEL,WAAW;gBACX,MAAM,mBAAmB,OAAO,QAAgB;oBAC9C,IAAI;wBACF,mBAAmB;wBAEnB,8BAA8B;wBAC9B,IAAI,YAAW,wBAAA,kCAAA,YAAa,EAAE,GAAE;4BAC9B,aAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,SAAS,CAAC;4BAClC,YAAO,CAAC,IAAI,CAAC;4BACb;wBACF;wBAEA,eAAe;wBACf,MAAM,WAAW,MAAM,qBAAW,CAAC,UAAU,CAAC;4BAAE;wBAAO;wBAEvD,kBAAkB;wBAClB,IAAI,SAAS,oBAAoB,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,EAAE,KAAK,QAAQ;4BACjF,aAAO,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,SAAS,CAAC;4BAEpC,+BAA+B;4BAC/B,IAAI,CAAA,yBAAA,mCAAA,aAAc,aAAa,MAAI,yBAAA,mCAAA,aAAc,aAAa,KAAI,iBAChE,IAAI;gCACF,MAAM,CAAC,aAAa,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;oCACnD,aAAa,aAAa;oCAC1B,aAAa,aAAa;iCAC3B;gCAED,cAAc;gCACd,IAAI,eAAe,YAAY,EAAE,KAAK,QAAQ;oCAC5C,MAAM,gBAAgB;wCACpB,GAAG,YAAY;wCACf;wCACA;oCACF;oCAEA,+BAA+B;oCAC/B,WAAW;wCACT,YAAO,CAAC,IAAI,CAAC;oCACf,GAAG;gCACL,OAAO;oCACL,QAAQ,KAAK,CAAC;oCACd,aAAO,CAAC,KAAK,CAAC;gCAChB;4BACF,EAAE,OAAO,OAAO;gCACd,QAAQ,KAAK,CAAC,uBAAuB;gCACrC,aAAO,CAAC,KAAK,CAAC;4BAChB;iCAEA,8BAA8B;4BAC9B,YAAO,CAAC,IAAI,CAAC;wBAEjB,OAAO;4BACL,QAAQ,KAAK,CAAC;4BACd,aAAO,CAAC,KAAK,CAAC;wBAChB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,aAAO,CAAC,KAAK,CAAC;oBAChB,SAAU;wBACR,mBAAmB;oBACrB;gBACF;gBAEA,qBACE;;sCAEE,2BAAC;4BAAM,yBAAyB;gCAAE,QAAQ;4BAAO;;;;;;sCAEjD,2BAAC,UAAI;4BACH,WAAU;4BACV,OAAO;gCACL,cAAc;gCACd,WAAW;gCACX,QAAQ;gCACR,YAAY;4BACd;4BACA,qBACE,2BAAC,UAAI;gCAAC,SAAQ;gCAAgB,OAAM;0CAClC,cAAA,2BAAC;oCAAM,OAAO;oCAAG,OAAO;wCACtB,QAAQ;wCACR,YAAY;wCACZ,sBAAsB;wCACtB,qBAAqB;wCACrB,YAAY;oCACd;8CAAG;;;;;;;;;;;sCAMR,sBACC,2BAAC,WAAK;gCACJ,SAAQ;gCACR,aAAa;gCACb,MAAK;gCACL,QAAQ;gCACR,OAAO;oCAAE,cAAc;gCAAG;;;;;qDAG5B,2BAAC,UAAI;gCAAC,UAAU;0CACd,cAAA,2BAAC,UAAI;oCACH,YAAY;oCACZ,YAAY,CAAC;4CAyKI,aAkBA,cAkBA,cAkBA;6DA9Nf,2BAAC,UAAI,CAAC,IAAI;sDACR,cAAA,2BAAC,UAAI;gDACH,WAAU;gDACV,OAAO;oDACL,YAAY,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,GACnC,8CACA;oDACJ,cAAc;oDACd,WAAW,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,GAClC,uCACA;oDACJ,OAAO;oDACP,YAAY,CAAC,UAAU,EAAE,KAAK,SAAS,GAAG,YAAY,UAAU,CAAC;oDACjE,YAAY;oDACZ,QAAQ,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,GAC/B,sBACA;oDACJ,SAAS;oDACT,UAAU;oDACV,UAAU;gDACZ;gDACA,SAAS;gDACT,cAAc,CAAC;oDACb,IAAI,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,EAAE;wDAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wDAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oDACpC;gDACF;gDACA,cAAc,CAAC;oDACb,IAAI,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,EAAE;wDAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wDAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oDACpC;gDACF;0DAIA,cAAA,2BAAC,UAAI;oDAAC,SAAQ;oDAAgB,OAAM;oDAAS,OAAO;wDAAE,OAAO;oDAAO;;sEAElE,2BAAC,UAAI;4DAAC,QAAQ;4DAAC,KAAK;4DAAG,OAAO;gEAAE,MAAM;4DAAE;;8EAEtC,2BAAC,UAAI;oEAAC,OAAM;oEAAS,KAAK;;sFACxB,2BAAC;4EACC,OAAO;gFACL,QAAQ,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,GAAG,YAAY;gFAClD,SAAS;gFACT,cAAc;gFACd,YAAY;gFACZ,SAAS;gFACT,YAAY;gFACZ,KAAK;4EACP;4EACA,SAAS,IAAM,iBAAiB,KAAK,EAAE,EAAE,KAAK,IAAI;4EAClD,cAAc,CAAC;gFACb,IAAI,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,EAC7B,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;4EAEvC;4EACA,cAAc,CAAC;gFACb,IAAI,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,EAC7B,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;4EAEvC;;8FAEA,2BAAC;oFACC,MAAM;oFACN,OAAO;wFACL,UAAU;wFACV,OAAO,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,GAAG,YAAY;wFACjD,YAAY;oFACd;8FAEC,KAAK,IAAI;;;;;;8FAEZ,2BAAC,oBAAa;oFACZ,OAAO;wFACL,UAAU;wFACV,OAAO,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,GAAG,YAAY;wFACjD,eAAe;wFACf,SAAS;wFACT,YAAY;oFACd;;;;;;;;;;;;wEAKH,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,kBAC1B,2BAAC;4EAAK,OAAO;gFACX,YAAY;gFACZ,OAAO;gFACP,SAAS;gFACT,cAAc;gFACd,UAAU;gFACV,YAAY;4EACd;sFAAG;;;;;;wEAKJ,oBAAoB,KAAK,EAAE,kBAC1B,2BAAC,UAAI;4EAAC,OAAM;4EAAS,KAAK;;8FACxB,2BAAC,UAAI;oFAAC,MAAK;;;;;;8FACX,2BAAC;oFAAK,OAAO;wFAAE,UAAU;wFAAI,OAAO;oFAAO;8FAAG;;;;;;;;;;;;;;;;;;8EAMpD,2BAAC,UAAI;oEAAC,OAAM;oEAAS,KAAK;;sFACxB,2BAAC,aAAO;4EAAC,OAAO,CAAC,MAAM,EAAE,IAAI,KAAK,KAAK,SAAS,EAAE,cAAc,CAAC,SAAS,CAAC;sFACzE,cAAA,2BAAC,UAAI;gFAAC,OAAM;gFAAS,KAAK;;kGACxB,2BAAC,0BAAmB;wFAAC,OAAO;4FAAE,OAAO;4FAAW,UAAU;wFAAG;;;;;;kGAC7D,2BAAC;wFAAK,OAAO;4FAAE,UAAU;4FAAI,OAAO;wFAAU;kGAC3C,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;sFAKnD,2BAAC,aAAO;4EAAC,OAAO,CAAC,MAAM,EAAE,KAAK,WAAW,CAAC,CAAC,CAAC;sFAC1C,cAAA,2BAAC,UAAI;gFAAC,OAAM;gFAAS,KAAK;;kGACxB,2BAAC,mBAAY;wFAAC,OAAO;4FAAE,OAAO;4FAAW,UAAU;wFAAG;;;;;;kGACtD,2BAAC;wFAAK,OAAO;4FAAE,UAAU;4FAAI,OAAO;wFAAU;;4FAC3C,KAAK,WAAW;4FAAC;;;;;;;;;;;;;;;;;;sFAMxB,2BAAC;4EAAK,OAAO;gFACX,YAAY,KAAK,SAAS,GAAG,YAAY;gFACzC,OAAO;gFACP,SAAS;gFACT,cAAc;gFACd,UAAU;gFACV,YAAY;gFACZ,SAAS;gFACT,YAAY;gFACZ,KAAK;4EACP;sFACG,KAAK,SAAS,iBACb;;kGACE,2BAAC,oBAAa;wFAAC,OAAO;4FAAE,UAAU;wFAAE;;;;;;oFAAK;;6GAI3C;;kGACE,2BAAC,mBAAY;wFAAC,OAAO;4FAAE,UAAU;wFAAE;;;;;;oFAAK;;;;;;;;;;;;;;;;;;;;sEASlD,2BAAC,UAAI;4DAAC,KAAK;4DAAG,OAAM;;8EAElB,2BAAC;oEAAI,OAAO;wEACV,YAAY;wEACZ,QAAQ;wEACR,cAAc;wEACd,SAAS;wEACT,WAAW;wEACX,UAAU;oEACZ;8EACE,cAAA,2BAAC,UAAI;wEAAC,QAAQ;wEAAC,OAAM;wEAAS,KAAK;;0FACjC,2BAAC,kBAAW;gFAAC,OAAO;oFAAE,OAAO;oFAAW,UAAU;gFAAG;;;;;;0FACrD,2BAAC;gFAAK,MAAM;gFAAC,OAAO;oFAAE,UAAU;oFAAI,OAAO;oFAAW,YAAY;gFAAE;0FACjE,EAAA,cAAA,KAAK,KAAK,cAAV,kCAAA,YAAY,QAAQ,KAAI;;;;;;0FAE3B,2BAAC;gFAAK,OAAO;oFAAE,UAAU;oFAAG,OAAO;gFAAO;0FAAG;;;;;;;;;;;;;;;;;8EAKjD,2BAAC;oEAAI,OAAO;wEACV,YAAY;wEACZ,QAAQ;wEACR,cAAc;wEACd,SAAS;wEACT,WAAW;wEACX,UAAU;oEACZ;8EACE,cAAA,2BAAC,UAAI;wEAAC,QAAQ;wEAAC,OAAM;wEAAS,KAAK;;0FACjC,2BAAC,mBAAY;gFAAC,OAAO;oFAAE,OAAO;oFAAW,UAAU;gFAAG;;;;;;0FACtD,2BAAC;gFAAK,MAAM;gFAAC,OAAO;oFAAE,UAAU;oFAAI,OAAO;oFAAW,YAAY;gFAAE;0FACjE,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,SAAS,KAAI;;;;;;0FAE5B,2BAAC;gFAAK,OAAO;oFAAE,UAAU;oFAAG,OAAO;gFAAO;0FAAG;;;;;;;;;;;;;;;;;8EAKjD,2BAAC;oEAAI,OAAO;wEACV,YAAY;wEACZ,QAAQ;wEACR,cAAc;wEACd,SAAS;wEACT,WAAW;wEACX,UAAU;oEACZ;8EACE,cAAA,2BAAC,UAAI;wEAAC,QAAQ;wEAAC,OAAM;wEAAS,KAAK;;0FACjC,2BAAC,gCAAyB;gFAAC,OAAO;oFAAE,OAAO;oFAAW,UAAU;gFAAG;;;;;;0FACnE,2BAAC;gFAAK,MAAM;gFAAC,OAAO;oFAAE,UAAU;oFAAI,OAAO;oFAAW,YAAY;gFAAE;0FACjE,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,QAAQ,KAAI;;;;;;0FAE3B,2BAAC;gFAAK,OAAO;oFAAE,UAAU;oFAAG,OAAO;gFAAO;0FAAG;;;;;;;;;;;;;;;;;8EAKjD,2BAAC;oEAAI,OAAO;wEACV,YAAY;wEACZ,QAAQ;wEACR,cAAc;wEACd,SAAS;wEACT,WAAW;wEACX,UAAU;oEACZ;8EACE,cAAA,2BAAC,UAAI;wEAAC,QAAQ;wEAAC,OAAM;wEAAS,KAAK;;0FACjC,2BAAC,gCAAyB;gFAAC,OAAO;oFAAE,OAAO;oFAAW,UAAU;gFAAG;;;;;;0FACnE,2BAAC;gFAAK,MAAM;gFAAC,OAAO;oFAAE,UAAU;oFAAI,OAAO;oFAAW,YAAY;gFAAE;0FACjE,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,OAAO,KAAI;;;;;;0FAE1B,2BAAC;gFAAK,OAAO;oFAAE,UAAU;oFAAG,OAAO;gFAAO;0FAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAcvE;eAlXM;;oBAOsC,aAAQ;;;iBAP9C;gBAoXN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDraD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,8BAA6B;YAAC;SAAmB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;YAAS;SAAyB;QAAC,mCAAkC;YAAC;YAAS;SAAyB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,iDAAgD;YAAC;SAAgD;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;IAAA;;AACh6B"}