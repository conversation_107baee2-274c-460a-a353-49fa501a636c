import {
  Bar<PERSON>hartOutlined,
  CheckOutlined,
  EditOutlined,
  LogoutOutlined,
  MailOutlined,
  PhoneOutlined,
  SettingOutlined,
  TagOutlined,
  UserOutlined,
} from "@ant-design/icons";
import {
  Button,
  Card,
  Col,
  Divider,
  Dropdown,
  Flex,
  Form,
  Input,
  Modal,
  Row,
  Space,
  Steps,
  Tag,
  Tooltip,
  Typography,
  Spin,
  Alert,
  Avatar,
} from "antd";
import React, { useState, useEffect } from "react";
import { UserService } from "@/services/user";
import { AuthService } from "@/services";
import { useModel, history } from '@umijs/max';
import type { UserPersonalStatsResponse, UserProfileDetailResponse } from "@/types/api";
 
const { Title, Text } = Typography;
const { Step } = Steps;

const UserProfileCard: React.FC = () => {
  // 用户详细信息状态
  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({
    name: "",
    position: "",
    email: "",
    phone: "",
    telephone: "",
    registerDate: "",
    lastLoginTime: "",
    lastLoginTeam: "",
    teamCount: 0,
    avatar: "",
  });
  const [userInfoLoading, setUserInfoLoading] = useState(true);
  const [userInfoError, setUserInfoError] = useState<string | null>(null);

  // 个人统计数据状态
  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({
    vehicles: 0,
    personnel: 0,
    warnings: 0,
    alerts: 0,
  });
  const [statsLoading, setStatsLoading] = useState(true);
  const [statsError, setStatsError] = useState<string | null>(null);

  // 订阅计划数据
  const subscriptionPlans = [
    {
      id: "basic",
      name: "基础版",
      price: 0,
      description: "适合小团队使用",
      features: ["最多5个团队", "最多20辆车辆", "基础安全监控", "基本报告功能"],
    },
    {
      id: "professional",
      name: "专业版",
      price: 199,
      description: "适合中小型企业",
      features: [
        "最多20个团队",
        "最多100辆车辆",
        "高级安全监控",
        "详细分析报告",
        "设备状态预警",
        "优先技术支持",
      ],
    },
    {
      id: "enterprise",
      name: "企业版",
      price: 499,
      description: "适合大型企业",
      features: [
        "不限团队数量",
        "不限车辆数量",
        "AI安全分析",
        "实时监控告警",
        "定制化报告",
        "专属客户经理",
        "24/7技术支持",
      ],
    },
  ];

  // 当前订阅信息
  const currentSubscription = {
    planId: "basic",
    expires: "2025-12-31",
  };

  // 状态管理
  const [editProfileModalVisible, setEditProfileModalVisible] = useState(false);
  const [subscriptionModalVisible, setSubscriptionModalVisible] =
    useState(false);
  const [logoutModalVisible, setLogoutModalVisible] = useState(false);
  const [logoutLoading, setLogoutLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [editProfileForm] = Form.useForm();

  const { setInitialState } = useModel('@@initialState');

  // 获取用户数据
  useEffect(() => {
    console.log('UserProfileCard: useEffect 开始执行');

    const fetchUserData = async () => {
      try {
        console.log('UserProfileCard: 开始获取用户数据');

        // 分别获取用户详细信息和统计数据，避免一个失败影响另一个
        const userDetailPromise = UserService.getUserProfileDetail().catch(error => {
          console.error('获取用户详细信息失败:', error);
          setUserInfoError('获取用户详细信息失败，请稍后重试');
          return null;
        });

        const statsPromise = UserService.getUserPersonalStats().catch(error => {
          console.error('获取统计数据失败:', error);
          setStatsError('获取统计数据失败，请稍后重试');
          return null;
        });

        const [userDetail, stats] = await Promise.all([userDetailPromise, statsPromise]);

        if (userDetail) {
          console.log('UserProfileCard: 获取到用户详细信息:', userDetail);
          setUserInfo(userDetail);
          setUserInfoError(null);
        }

        if (stats) {
          console.log('UserProfileCard: 获取到统计数据:', stats);
          setPersonalStats(stats);
          setStatsError(null);
        }

      } catch (error) {
        console.error('获取用户数据时发生未知错误:', error);
        setUserInfoError('获取用户数据失败，请刷新页面重试');
        setStatsError('获取统计数据失败，请刷新页面重试');
      } finally {
        setUserInfoLoading(false);
        setStatsLoading(false);
      }
    };

    fetchUserData();
  }, []);

  // 退出登录处理函数
  const handleLogout = async () => {
    try {
      setLogoutLoading(true);

      // 调用退出登录API
      await AuthService.logout();

      // 清除 initialState
      if (setInitialState) {
        await setInitialState({
          currentUser: undefined,
          currentTeam: undefined,
        });
      }

      // 跳转到登录页面
      history.push('/user/login');

    } catch (error) {
      console.error('退出登录失败:', error);
      // 即使API调用失败，也要清除本地状态并跳转
      if (setInitialState) {
        await setInitialState({
          currentUser: undefined,
          currentTeam: undefined,
        });
      }
      history.push('/user/login');
    } finally {
      setLogoutLoading(false);
      setLogoutModalVisible(false);
    }
  };

  return (
    <>
      {/* 用户信息主卡片 */}
      {userInfoError ? (
        <Alert
          message="用户信息加载失败"
          description={userInfoError}
          type="error"
          showIcon
          style={{ marginBottom: 24 }}
        />
      ) : (
        <Spin spinning={userInfoLoading}>
          {/* 使用 Card 组件替代自定义 div */}
          <Card
            style={{
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              borderRadius: 16,
              color: "white",
              position: "relative",
              overflow: "hidden",
              height: 140,
              border: "none",
            }}
            styles={{ body: { padding: 24, height: "100%" } }}
          >
              {/* 操作按钮区域 - 使用 Space 组件 */}
              <Space
                style={{
                  position: "absolute",
                  top: 16,
                  right: 16,
                  zIndex: 20,
                }}
                size={8}
              >
                <Tooltip title="设置">
                  <Dropdown
                    menu={{
                      items: [
                        {
                          key: "editProfile",
                          icon: <EditOutlined />,
                          label: "修改资料",
                          onClick: () => {
                            setEditProfileModalVisible(true);
                            setCurrentStep(0);
                            editProfileForm.setFieldsValue({
                              name: userInfo.name,
                              email: userInfo.email,
                              telephone: userInfo.phone || userInfo.telephone,
                            });
                          },
                        },
                        {
                          key: "subscription",
                          icon: <TagOutlined />,
                          label: "订阅套餐",
                          onClick: () => setSubscriptionModalVisible(true),
                        },
                      ],
                    }}
                    trigger={["click"]}
                    placement="bottomRight"
                  >
                    <Button
                      type="text"
                      shape="circle"
                      icon={<SettingOutlined />}
                      style={{
                        color: "rgba(255,255,255,0.9)",
                        backgroundColor: "rgba(255,255,255,0.15)",
                        border: "none",
                        transition: "all 0.2s",
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = "rgba(255,255,255,0.25)";
                        e.currentTarget.style.color = "white";
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = "rgba(255,255,255,0.15)";
                        e.currentTarget.style.color = "rgba(255,255,255,0.9)";
                      }}
                    />
                  </Dropdown>
                </Tooltip>

                <Tooltip title="退出登录">
                  <Button
                    type="text"
                    shape="circle"
                    icon={<LogoutOutlined />}
                    onClick={() => setLogoutModalVisible(true)}
                    style={{
                      color: "rgba(255,255,255,0.9)",
                      backgroundColor: "rgba(255,255,255,0.15)",
                      border: "none",
                      transition: "all 0.2s",
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = "rgba(255,77,79,0.3)";
                      e.currentTarget.style.color = "#ff4d4f";
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = "rgba(255,255,255,0.15)";
                      e.currentTarget.style.color = "rgba(255,255,255,0.9)";
                    }}
                  />
                </Tooltip>
              </Space>
              {/* 装饰性背景元素 */}
              <div
                style={{
                  position: "absolute",
                  top: -25,
                  right: -25,
                  width: 100,
                  height: 100,
                  background: "rgba(255,255,255,0.1)",
                  borderRadius: "50%",
                }}
              />
              <div
                style={{
                  position: "absolute",
                  bottom: -30,
                  left: -30,
                  width: 80,
                  height: 80,
                  background: "rgba(255,255,255,0.05)",
                  borderRadius: "50%",
                }}
              />
              <div
                style={{
                  position: "absolute",
                  top: "50%",
                  right: "20%",
                  width: 60,
                  height: 60,
                  background: "rgba(255,255,255,0.03)",
                  borderRadius: "50%",
                  transform: "translateY(-50%)",
                }}
              />

              {/* 主要内容区域 - 使用 Flex 组件 */}
              <Flex
                align="center"
                gap={32}
                style={{ position: "relative", zIndex: 1, width: "100%" }}
              >
                {/* 左侧：用户基本信息区域 */}
                <Flex align="center" style={{ flex: "0 0 320px", minWidth: 320 }}>
                  {/* 用户头像 - 使用 Ant Design Avatar 组件 */}
                  <Avatar
                    size={64}
                    shape="square"
                    style={{
                      backgroundColor: "rgba(255,255,255,0.2)",
                      marginRight: 20,
                      fontSize: 24,
                      fontWeight: 600,
                      border: "2px solid rgba(255,255,255,0.3)",
                    }}
                  >
                    {userInfo.name ? userInfo.name.charAt(0).toUpperCase() : <UserOutlined />}
                  </Avatar>

                  {/* 用户信息 - 使用 Space 组件垂直布局 */}
                  <Space direction="vertical" size={4}>
                    <Title
                      level={3}
                      style={{
                        margin: 0,
                        color: "white",
                        fontSize: 22,
                        fontWeight: 600,
                      }}
                    >
                      {userInfo.name || "加载中..."}
                    </Title>

                    {/* 联系信息 - 使用 Space 组件垂直排列 */}
                    <Space direction="vertical" size={4}>
                      {userInfo.email && (
                        <Space size={6} align="center">
                          <MailOutlined style={{ fontSize: 13, color: "rgba(255,255,255,0.9)" }} />
                          <Text style={{ color: "rgba(255,255,255,0.9)", fontSize: 12 }}>
                            {userInfo.email}
                          </Text>
                        </Space>
                      )}
                      {(userInfo.phone || userInfo.telephone) && (
                        <Space size={6} align="center">
                          <PhoneOutlined style={{ fontSize: 13, color: "rgba(255,255,255,0.9)" }} />
                          <Text style={{ color: "rgba(255,255,255,0.9)", fontSize: 12 }}>
                            {userInfo.phone || userInfo.telephone}
                          </Text>
                        </Space>
                      )}
                    </Space>

                    {/* 注册日期 */}
                    {userInfo.registerDate && (
                      <Text style={{ fontSize: 13, color: "rgba(255,255,255,0.8)", fontWeight: 500 }}>
                        注册于 {userInfo.registerDate}
                      </Text>
                    )}
                  </Space>
                </Flex>

                {/* 分割线 */}
                <Divider
                  type="vertical"
                  style={{
                    height: "80px",
                    borderColor: "rgba(255,255,255,0.3)",
                    margin: "0 16px",
                  }}
                />

                {/* 中间：数据统计概览 - 使用 Flex 组件 */}
                <Flex
                  vertical
                  style={{
                    flex: "1 1 auto" 
                  }}
                >
                  {/* 标题区域 - 使用 Space 组件 */}
                  <Space
                    align="center"
                    style={{
                      justifyContent: "center",
                      marginBottom: 30,
                      height: 20,
                    }}
                  >
                    <BarChartOutlined
                      style={{
                        fontSize: 16,
                        color: "rgba(255,255,255,0.9)",
                      }}
                    />
                    <Text style={{ color: "rgba(255,255,255,0.9)", fontSize: 14, fontWeight: 600, lineHeight: 1 }}>
                      数据概览
                    </Text>
                  </Space>

                  {statsError ? (
                    <Text style={{ fontSize: 12, color: "rgba(255,255,255,0.8)" }}>
                      数据加载失败
                    </Text>
                  ) : (
                    <Spin spinning={statsLoading}>
                      {/* 统计数据 - 使用 Flex 组件和自定义样式 */}
                      <Flex justify="space-around" align="center">
                        <div style={{ textAlign: 'center' }}>
                          <div style={{
                            fontSize: 20,
                            fontWeight: 700,
                            color: "white",
                            lineHeight: 1,
                          }}>
                            {personalStats.vehicles}
                          </div>
                          <div style={{
                            fontSize: 13,
                            color: "rgba(255,255,255,0.8)",
                            marginTop: 3,
                          }}>
                            车辆
                          </div>
                        </div>
                        <div style={{ textAlign: 'center' }}>
                          <div style={{
                            fontSize: 20,
                            fontWeight: 700,
                            color: "white",
                            lineHeight: 1,
                          }}>
                            {personalStats.personnel}
                          </div>
                          <div style={{
                            fontSize: 13,
                            color: "rgba(255,255,255,0.8)",
                            marginTop: 3,
                          }}>
                            人员
                          </div>
                        </div>
                        <div style={{ textAlign: 'center' }}>
                          <div style={{
                            fontSize: 20,
                            fontWeight: 700,
                            color: "white",
                            lineHeight: 1,
                          }}>
                            {personalStats.warnings}
                          </div>
                          <div style={{
                            fontSize: 13,
                            color: "rgba(255,255,255,0.8)",
                            marginTop: 3,
                          }}>
                            预警
                          </div>
                        </div>
                        <div style={{ textAlign: 'center' }}>
                          <div style={{
                            fontSize: 20,
                            fontWeight: 700,
                            color: "white",
                            lineHeight: 1,
                          }}>
                            {personalStats.alerts}
                          </div>
                          <div style={{
                            fontSize: 13,
                            color: "rgba(255,255,255,0.8)",
                            marginTop: 3,
                          }}>
                            告警
                          </div>
                        </div>
                      </Flex>
                    </Spin>
                  )}
                </Flex>

                {/* 分割线 */}
                <Divider
                  type="vertical"
                  style={{
                    height: "80px",
                    borderColor: "rgba(255,255,255,0.3)",
                    margin: "0 16px",
                  }}
                />

                {/* 右侧：最近活动信息 - 使用 Space 组件 */}
                <Space
                  direction="vertical"
                  size={10}
                  style={{
                    flex: "0 0 300px",
                    paddingLeft: 20,
                    width: 300,
                    minWidth: 300,
                  }}
                >
                  <Space direction="vertical" size={4}>
                    <Text style={{ fontSize: 12, color: "rgba(255,255,255,0.8)", fontWeight: 500 }}>
                      最后登录时间
                    </Text>
                    <Text style={{ fontSize: 14, color: "white", fontWeight: 600, lineHeight: 1.3 }}>
                      {userInfo.lastLoginTime || "暂无记录"}
                    </Text>
                  </Space>
                  <Space direction="vertical" size={4}>
                    <Text style={{ fontSize: 12, color: "rgba(255,255,255,0.8)", fontWeight: 500 }}>
                      最后登录团队
                    </Text>
                    <Text style={{ fontSize: 14, color: "white", fontWeight: 600, lineHeight: 1.3 }}>
                      {userInfo.lastLoginTeam || "暂无记录"}
                    </Text>
                  </Space>
                </Space>
              </Flex>
            </Card>
          </Spin>
        )}

      {/* 修改资料模态框 */}
      <Modal
        title="修改个人资料"
        open={editProfileModalVisible}
        onCancel={() => {
          setEditProfileModalVisible(false);
          setCurrentStep(0);
        }}
        footer={[
          currentStep === 1 && (
            <Button key="back" onClick={() => setCurrentStep(0)}>
              上一步
            </Button>
          ),
          <Button
            key="submit"
            type="primary"
            onClick={() => {
              if (currentStep === 0) {
                editProfileForm.validateFields().then(() => {
                  setCurrentStep(1);
                });
              } else {
                editProfileForm.validateFields().then((values) => {
                  console.log("个人资料表单值:", values);
                  // 提交表单，这里简化处理，只输出到控制台
                  setEditProfileModalVisible(false);
                  setCurrentStep(0);
                });
              }
            }}
          >
            {currentStep === 0 ? "下一步" : "确定"}
          </Button>,
        ]}
      >
        <Steps current={currentStep} style={{ marginBottom: 16 }}>
          <Step title="填写信息" />
          <Step title="安全验证" />
        </Steps>

        <Form form={editProfileForm} layout="vertical" requiredMark={false}>
          {currentStep === 0 ? (
            <>
              <Form.Item
                name="name"
                label="用户名"
                rules={[{ required: true, message: "请输入用户名" }]}
              >
                <Input placeholder="请输入用户名" />
              </Form.Item>
              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { required: true, message: "请输入邮箱地址" },
                  { type: "email", message: "请输入有效的邮箱地址" },
                ]}
              >
                <Input placeholder="请输入邮箱地址" />
              </Form.Item>
              <Form.Item
                name="telephone"
                label="手机号"
                rules={[
                  { required: true, message: "请输入手机号" },
                  { pattern: /^1\d{10}$/, message: "请输入有效的手机号" },
                ]}
              >
                <Input placeholder="请输入手机号" />
              </Form.Item>
            </>
          ) : (
            /* 验证码步骤 - 使用 Space 组件 */
            <Space direction="vertical" align="center" style={{ width: "100%" }}>
              <Text style={{ margin: "12px 0" }}>
                验证码已发送至您的手机号{" "}
                <Text strong>{editProfileForm.getFieldValue("telephone")}</Text>
              </Text>
              <Form.Item
                name="verificationCode"
                label="验证码"
                rules={[{ required: true, message: "请输入验证码" }]}
                style={{ textAlign: "center" }}
              >
                <Input
                  placeholder="请输入6位验证码"
                  maxLength={6}
                  style={{ width: "50%", textAlign: "center" }}
                />
              </Form.Item>
              <Button type="link" style={{ padding: 0 }}>
                重新发送验证码
              </Button>
            </Space>
          )}
        </Form>
      </Modal>

      {/* 订阅套餐模态框 */}
      <Modal
        title="订阅套餐"
        open={subscriptionModalVisible}
        onCancel={() => setSubscriptionModalVisible(false)}
        footer={null}
        width={800}
      >
        {/* 当前套餐信息 - 使用 Card 组件 */}
        <Card
          size="small"
          style={{
            background: "#f9f9f9",
            marginBottom: 16,
          }}
        >
          <Flex justify="space-between" align="center">
            <Text strong>当前套餐: </Text>
            <Tag color="green" style={{ marginLeft: 8, fontSize: 13 }}>
              {
                subscriptionPlans.find(
                  (p) => p.id === currentSubscription.planId
                )?.name
              }
            </Tag>
            <Text type="secondary">
              到期时间: {currentSubscription.expires}
            </Text>
          </Flex>
        </Card>

        <Row gutter={24}>
          {subscriptionPlans.map((plan) => (
            <Col span={8} key={plan.id}>
              {/* 套餐卡片 - 使用 Card 组件 */}
              <Card
                style={{
                  height: "100%",
                  border: `1px solid ${
                    plan.id === currentSubscription.planId
                      ? "#52c41a"
                      : "#d9d9d9"
                  }`,
                  position: "relative",
                }}
                styles={{ body: { padding: 16 } }}
              >
                {plan.id === currentSubscription.planId && (
                  <Tag
                    color="green"
                    style={{
                      position: "absolute",
                      top: -10,
                      right: -10,
                      borderRadius: 2,
                      boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                    }}
                  >
                    当前套餐
                  </Tag>
                )}
                <Title
                  level={4}
                  style={{ textAlign: "center", margin: "12px 0 8px" }}
                >
                  {plan.name}
                </Title>
                <Flex vertical align="center" style={{ marginBottom: 12 }}>
                  {plan.price > 0 ? (
                    <>
                      <Title level={2} style={{ marginBottom: 0 }}>
                        ¥{plan.price}
                      </Title>
                      <Text type="secondary">/月</Text>
                    </>
                  ) : (
                    <Title
                      level={2}
                      style={{ color: "#52c41a", marginBottom: 0 }}
                    >
                      免费
                    </Title>
                  )}
                  <Text type="secondary" style={{ marginTop: 4 }}>
                    {plan.description}
                  </Text>
                </Flex>

  

                {/* 功能列表 - 使用 Space 组件 */}
                <Space direction="vertical" size={6} style={{ minHeight: 170, width: "100%" }}>
                  {plan.features.map((feature, index) => (
                    <Space key={index} align="start">
                      <CheckOutlined
                        style={{
                          color: "#52c41a",
                          marginTop: 4,
                        }}
                      />
                      <Text>{feature}</Text>
                    </Space>
                  ))}
                </Space>

                {plan.id !== currentSubscription.planId ? (
                  <Button
                    type="primary"
                    block
                    style={{
                      marginTop: 12,
                      boxShadow: "0 2px 8px rgba(24, 144, 255, 0.3)",
                    }}
                    onClick={() => {
                      console.log("选择套餐:", plan);
                      setSubscriptionModalVisible(false);
                    }}
                  >
                    立即订阅
                  </Button>
                ) : (
                  <Button
                    block
                    style={{
                      marginTop: 12,
                      background: "#f6ffed",
                      borderColor: "#b7eb8f",
                      color: "#389e0d",
                    }}
                    disabled
                  >
                    当前套餐
                  </Button>
                )}
              </Card>
            </Col>
          ))}
        </Row>

        <Flex justify="center" style={{ marginTop: 20 }}>
          <Text type="secondary">订阅服务自动续费，可随时取消</Text>
        </Flex>
      </Modal>

      {/* 退出登录确认模态框 */}
      <Modal
        title="确认退出登录"
        open={logoutModalVisible}
        onCancel={() => setLogoutModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setLogoutModalVisible(false)}>
            取消
          </Button>,
          <Button
            key="confirm"
            type="primary"
            danger
            loading={logoutLoading}
            onClick={handleLogout}
          >
            确认退出
          </Button>,
        ]}
        width={400}
      >
        {/* 退出登录确认内容 - 使用 Space 组件 */}
        <Space direction="vertical" align="center" style={{ width: "100%", padding: "20px 0" }}>
          <LogoutOutlined style={{ fontSize: 48, color: '#ff4d4f' }} />
          <Text strong style={{ fontSize: 16 }}>您确定要退出登录吗？</Text>
          <Text type="secondary" style={{ textAlign: "center" }}>
            退出后您需要重新登录才能继续使用系统
          </Text>
        </Space>
      </Modal>
    </>
  );
};

export default UserProfileCard;