{"version": 3, "sources": ["src_pages_test_profile-consolidated_index_tsx-async.14466178166597315773.hot-update.js", "src/pages/test/profile-consolidated/index.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/test/profile-consolidated/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='3121196459078985671';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/friend/index.tsx\":[\"p__friend__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"common\",\"p__subscription__index\"],\"src/pages/team/detail/index.tsx\":[\"common\",\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"p__team__index\"],\"src/pages/test/profile-consolidated/index.tsx\":[\"src/pages/test/profile-consolidated/index.tsx\"],\"src/pages/user/index.tsx\":[\"common\",\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import React from \"react\";\nimport { <PERSON><PERSON>, <PERSON>, Col, Row } from \"antd\";\n \nimport TodoManagement from './TodoManagement';\nimport TeamListCard from './TeamListCard';\nimport UserProfileCard from \"./UserProfileCard\";\n\nconst FleetManagementDashboard: React.FC = () => {\n  return (\n    <div style={{ minHeight: \"100vh\", background: \"#f5f8ff\", padding: \"24px\" }}>\n      {/* 大的容器区域 */}\n      <Card\n        style={{\n          width: \"100%\",\n          minHeight: \"calc(100vh - 48px)\",\n          borderRadius: \"12px\",\n          boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.1)\"\n        }}\n        styles={{ body: { padding: \"24px\" } }}\n      >\n        <Row gutter={[24, 24]}>\n          {/* 个人信息卡片 */}\n          <Col xs={24}>\n            <UserProfileCard />\n          </Col>\n\n          {/* 待办事项 */}\n          <Col xs={24} lg={12}>\n            <TodoManagement />\n          </Col>\n\n          {/* 团队列表 */}\n          <Col xs={24} lg={12}>\n            <TeamListCard />\n          </Col>\n        </Row>\n      </Card>\n    </div>\n  );\n};\n\nexport default FleetManagementDashboard;"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,iDACA;IACE,SAAS;;;;;;wCCsCb;;;2BAAA;;;;;;;mFAzCkB;yCACqB;4FAEZ;0FACF;6FACG;;;;;;;;;YAE5B,MAAM,2BAAqC;gBACzC,qBACE,2BAAC;oBAAI,OAAO;wBAAE,WAAW;wBAAS,YAAY;wBAAW,SAAS;oBAAO;8BAEvE,cAAA,2BAAC,UAAI;wBACH,OAAO;4BACL,OAAO;4BACP,WAAW;4BACX,cAAc;4BACd,WAAW;wBACb;wBACA,QAAQ;4BAAE,MAAM;gCAAE,SAAS;4BAAO;wBAAE;kCAEpC,cAAA,2BAAC,SAAG;4BAAC,QAAQ;gCAAC;gCAAI;6BAAG;;8CAEnB,2BAAC,SAAG;oCAAC,IAAI;8CACP,cAAA,2BAAC,wBAAe;;;;;;;;;;8CAIlB,2BAAC,SAAG;oCAAC,IAAI;oCAAI,IAAI;8CACf,cAAA,2BAAC,uBAAc;;;;;;;;;;8CAIjB,2BAAC,SAAG;oCAAC,IAAI;oCAAI,IAAI;8CACf,cAAA,2BAAC,qBAAY;;;;;;;;;;;;;;;;;;;;;;;;;;YAMzB;iBAhCM;gBAkCN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDtCD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,8BAA6B;YAAC;SAAmB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;YAAS;SAAyB;QAAC,mCAAkC;YAAC;YAAS;SAAyB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,iDAAgD;YAAC;SAAgD;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;IAAA;;AACh6B"}