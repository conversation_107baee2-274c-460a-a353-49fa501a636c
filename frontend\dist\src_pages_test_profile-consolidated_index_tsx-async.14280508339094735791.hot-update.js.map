{"version": 3, "sources": ["src_pages_test_profile-consolidated_index_tsx-async.14280508339094735791.hot-update.js", "src/pages/test/profile-consolidated/index.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/test/profile-consolidated/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='17516296004174770197';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/friend/index.tsx\":[\"p__friend__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"common\",\"p__subscription__index\"],\"src/pages/team/detail/index.tsx\":[\"common\",\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"p__team__index\"],\"src/pages/test/profile-consolidated/index.tsx\":[\"src/pages/test/profile-consolidated/index.tsx\"],\"src/pages/user/index.tsx\":[\"common\",\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import React from \"react\";\nimport { <PERSON><PERSON>, <PERSON>, Col, Row } from \"antd\";\n \nimport TodoManagement from './TodoManagement';\nimport TeamListCard from './TeamListCard';\nimport UserProfileCard from \"./UserProfileCard\";\n\nconst FleetManagementDashboard: React.FC = () => {\n  return (\n    <div style={{ minHeight: \"100vh\", background: \"#f5f8ff\", padding: \"24px\" }}>\n      {/* 新的三栏布局 */}\n      <Row gutter={[32, 32]} style={{ height: \"calc(100vh - 48px)\" }}>\n        {/* 左侧栏 - 个人信息卡片 */}\n        <Col xs={24} md={8} lg={6} style={{ display: \"flex\", flexDirection: \"column\" }}>\n          <div style={{ flex: 1 }}>\n            <UserProfileCard />\n          </div>\n        </Col>\n\n        {/* 中间栏 - 待办事项 */}\n        <Col xs={24} md={8} lg={12} style={{ display: \"flex\", flexDirection: \"column\" }}>\n          <div style={{ flex: 1 }}>\n            <TodoManagement />\n          </div>\n        </Col>\n\n        {/* 右侧栏 - 团队列表 */}\n        <Col xs={24} md={8} lg={6} style={{ display: \"flex\", flexDirection: \"column\" }}>\n          <div style={{ flex: 1 }}>\n            <TeamListCard />\n          </div>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default FleetManagementDashboard;"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,iDACA;IACE,SAAS;;;;;;wCCkCb;;;2BAAA;;;;;;;mFArCkB;yCACqB;4FAEZ;0FACF;6FACG;;;;;;;;;YAE5B,MAAM,2BAAqC;gBACzC,qBACE,2BAAC;oBAAI,OAAO;wBAAE,WAAW;wBAAS,YAAY;wBAAW,SAAS;oBAAO;8BAEvE,cAAA,2BAAC,SAAG;wBAAC,QAAQ;4BAAC;4BAAI;yBAAG;wBAAE,OAAO;4BAAE,QAAQ;wBAAqB;;0CAE3D,2BAAC,SAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAG,IAAI;gCAAG,OAAO;oCAAE,SAAS;oCAAQ,eAAe;gCAAS;0CAC3E,cAAA,2BAAC;oCAAI,OAAO;wCAAE,MAAM;oCAAE;8CACpB,cAAA,2BAAC,wBAAe;;;;;;;;;;;;;;;0CAKpB,2BAAC,SAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAG,IAAI;gCAAI,OAAO;oCAAE,SAAS;oCAAQ,eAAe;gCAAS;0CAC5E,cAAA,2BAAC;oCAAI,OAAO;wCAAE,MAAM;oCAAE;8CACpB,cAAA,2BAAC,uBAAc;;;;;;;;;;;;;;;0CAKnB,2BAAC,SAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAG,IAAI;gCAAG,OAAO;oCAAE,SAAS;oCAAQ,eAAe;gCAAS;0CAC3E,cAAA,2BAAC;oCAAI,OAAO;wCAAE,MAAM;oCAAE;8CACpB,cAAA,2BAAC,qBAAY;;;;;;;;;;;;;;;;;;;;;;;;;;YAMzB;iBA5BM;gBA8BN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDlCD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,8BAA6B;YAAC;SAAmB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;YAAS;SAAyB;QAAC,mCAAkC;YAAC;YAAS;SAAyB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,iDAAgD;YAAC;SAAgD;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;IAAA;;AACh6B"}